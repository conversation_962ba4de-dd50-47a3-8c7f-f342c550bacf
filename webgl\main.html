<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
<!--    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>-->
    <script src="./echarts/echarts.min.js"></script>
    <!-- 引入图表配置文件 -->
    <script src="charts.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
      /**
       * 桂林智源 Unity WebGL 界面切换功能样式
       * 实现Unity 3D界面和电气拓扑图之间的切换功能
       */

      /* Unity包装容器 */
      .unity-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      /* 侧边箭头按钮 - 优化为低调设计 */
      .arrow-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 50px;
        height: 70px;
        /* 默认状态：几乎透明的背景，无边框，无阴影 */
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        color: rgba(255, 255, 255, 0.4);
        font-size: 20px;
        cursor: pointer;
        transition: all 0.4s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        /* 移除默认的backdrop-filter和box-shadow */
      }

      .arrow-btn:hover {
        /* 悬停时显示完整的视觉效果 */
        background: linear-gradient(135deg, rgba(26, 35, 50, 0.95), rgba(42, 52, 65, 0.95));
        border: 2px solid #00d4ff;
        color: #00d4ff;
        backdrop-filter: blur(10px);
        box-shadow: 0 6px 25px rgba(0, 212, 255, 0.6);
        transform: translateY(-50%) scale(1.1);
      }

      .arrow-btn:active {
        background: linear-gradient(135deg, #00d4ff, #0099cc);
        color: #0a0f1c;
        transform: translateY(-50%) scale(0.95);
        box-shadow: 0 2px 15px rgba(0, 212, 255, 0.8);
      }

      /* 左侧箭头按钮 */
      .arrow-btn.left {
        left: 15px;
        border-radius: 12px 4px 4px 12px;
      }

      /* 右侧箭头按钮 */
      .arrow-btn.right {
        right: 15px;
        border-radius: 4px 12px 12px 4px;
      }

      /* 箭头按钮图标动画 */
      .arrow-btn i {
        transition: transform 0.3s ease;
      }

      .arrow-btn:hover i {
        transform: scale(1.2);
      }

      /* 当前视图状态指示 - 优化为低调设计 */
      .arrow-btn.current-view {
        /* 默认状态：稍微明显一点但仍然低调 */
        background: rgba(0, 255, 136, 0.1);
        border: 1px solid rgba(0, 255, 136, 0.3);
        color: rgba(0, 255, 136, 0.6);
        /* 移除默认的box-shadow */
      }

      .arrow-btn.current-view:hover {
        /* 悬停时显示完整的当前视图效果 */
        background: linear-gradient(135deg, #00ff88, #00d4ff);
        border: 2px solid #00ff88;
        color: #0a0f1c;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 20px rgba(0, 255, 136, 0.4);
        transform: translateY(-50%) scale(1.05);
      }

      /* Unity容器样式调整 */
      .unity-container {
        position: absolute;
        width: 100%;
        height: 100%;
        transition: opacity 0.5s ease;
        opacity: 1;
        visibility: visible;
      }

      /* 电气拓扑iframe容器 */
      .topology-container {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        visibility: hidden;
        transition: all 0.5s ease;
      }

      .topology-iframe {
        width: 100%;
        height: 100%;
        border: none;
        background: #0a0f1c;
      }

      /* 显示状态控制 - 支持多页面循环切换 */
      .unity-wrapper .unity-container,
      .unity-wrapper .topology-container {
        opacity: 0;
        visibility: hidden;
      }

      /* Unity 3D界面 */
      .unity-wrapper.show-unity .unity-container {
        opacity: 1;
        visibility: visible;
      }

      /* 电气拓扑界面 */
      .unity-wrapper.show-electrical-topology .electrical-topology-container {
        opacity: 1;
        visibility: visible;
      }

      /* 水冷拓扑界面 */
      .unity-wrapper.show-cooling-topology .cooling-topology-container {
        opacity: 1;
        visibility: visible;
      }

      /* 加载动画覆盖层 */
      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(10, 15, 28, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 20px;
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #3a4a5c;
        border-top: 3px solid #00d4ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        color: #b8c5d6;
        font-size: 14px;
        font-family: 'Microsoft YaHei', sans-serif;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* 响应式设计 */
      @media (max-width: 1366px) {
        .arrow-btn {
          width: 45px;
          height: 60px;
          font-size: 18px;
        }

        .arrow-btn.left {
          left: 12px;
        }

        .arrow-btn.right {
          right: 12px;
        }
      }

      @media (max-width: 768px) {
        .arrow-btn {
          width: 40px;
          height: 55px;
          font-size: 16px;
        }

        .arrow-btn.left {
          left: 8px;
        }

        .arrow-btn.right {
          right: 8px;
        }
      }
    </style>
  </head>
  <body>
    <div id="main-container" class="app-container">
      <!-- 顶部导航栏 -->
      <header class="header">
        <!-- 区域1：项目标识 -->
        <div class="header-left">
          <div class="logo">
            <img src="logo.png" alt="桂林智源" class="logo-image">
            <span class="logo-text">桂林智源</span>
          </div>
          <div class="system-title">SVG 数字化系统</div>
        </div>

        <!-- 区域2：系统标题和菜单 -->
        <div class="header-center">
          <div class="system-title-section">
            <h1 class="main-title" onclick="toggleMainMenu()">中科院等离子极向场无功补偿SVG-B3</h1>
            <button class="menu-btn" onclick="toggleMainMenu()">
              <i class="fas fa-bars"></i>
              <span>菜单</span>
            </button>
          </div>

          <!-- 主菜单下拉框 -->
          <div class="main-menu-dropdown" id="mainMenuDropdown">
            <div class="menu-item" onclick="navigateToModule('realtime-curve')" style="display: none;">
              <i class="fas fa-chart-line"></i>
              <span>实时曲线</span>
            </div>
            <div class="menu-item" onclick="navigateToModule('history-record')" style="display: none;">
              <i class="fas fa-history"></i>
              <span>历史记录</span>
            </div>
            <div class="menu-item" onclick="navigateToModule('history-event')">
              <i class="fas fa-calendar-alt"></i>
              <span>历史事件</span>
            </div>
            <div class="menu-item" onclick="navigateToModule('parameter-curve')">
              <i class="fas fa-chart-area"></i>
              <span>参数曲线</span>
            </div>
            <div class="menu-item" onclick="navigateToModule('dsp')">
              <i class="fas fa-microchip"></i>
              <span>DSP</span>
            </div>
            <div class="menu-item" onclick="navigateToModule('fault-wave')">
              <i class="fas fa-wave-square"></i>
              <span>故障录波</span>
            </div>
            <div class="menu-item" onclick="navigateToModule('version-info')">
              <i class="fas fa-info-circle"></i>
              <span>版本信息</span>
            </div>
          </div>
        </div>

        <!-- 区域3：系统信息 -->
        <div class="header-right">
          <div class="system-info-panel">
            <div class="time-display" id="currentTime"></div>
            <div class="connection-status">
              <div class="status-indicator online">
                <i class="fas fa-circle"></i>
              </div>
              <span>连接状态正常</span>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容区域 -->
      <main class="main-content">
        <!-- 区域4：左栏 - 系统状态与参数 -->
        <aside class="left-panel">
          <!-- 电气系统区域 - 占用50%高度 -->
          <div class="system-status-area">
            <div class="panel-header">
              <h3><i class="fas fa-bolt"></i>电气系统</h3>
            </div>

            <!-- 系统运行状态 - 5种状态显示 -->
            <div class="status-section">
              <h4><i class="fas fa-power-off"></i>系统状态</h4>
              <div class="status-grid-five">
                <div class="status-item" data-status="ready">
                  <div class="status-indicator ready">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">就绪</span>
                </div>

                <div class="status-item" data-status="fault">
                  <div class="status-indicator fault">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">故障</span>
                </div>

                <div class="status-item" data-status="charging">
                  <div class="status-indicator charging">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">充电</span>
                </div>

                <div class="status-item" data-status="waiting">
                  <div class="status-indicator waiting">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">合高压等待</span>
                </div>

                <div class="status-item active" data-status="running">
                  <div class="status-indicator running">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">运行</span>
                </div>
              </div>
            </div>

            <!-- 系统关键参数 - 2-3列网格布局 -->
            <div class="parameters-section">
              <h4><i class="fas fa-chart-bar"></i>关键参数</h4>
              <div class="parameter-grid-compact">
                <div class="parameter-item" onclick="showParameterDetails('load-active-current')">
                  <div class="parameter-content">
                    <div class="parameter-label">负载有功电流:</div>
                    <div class="parameter-value">0.0000 A</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('load-reactive-current')">
                  <div class="parameter-content">
                    <div class="parameter-label">负载无功电流:</div>
                    <div class="parameter-value">0.0000 A</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('svg-active-current')">
                  <div class="parameter-content">
                    <div class="parameter-label">SVG有功电流:</div>
                    <div class="parameter-value">0.0000 A</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('svg-reactive-current')">
                  <div class="parameter-content">
                    <div class="parameter-label">SVG无功电流:</div>
                    <div class="parameter-value">0.0000 A</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('reactive-power')">
                  <div class="parameter-content">
                    <div class="parameter-label">无功功率:</div>
                    <div class="parameter-value">MVAr</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('power-factor')">
                  <div class="parameter-content">
                    <div class="parameter-label">功率因数:</div>
                    <div class="parameter-value"></div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('bus-voltage-uab')">
                  <div class="parameter-content">
                    <div class="parameter-label">母线电压Uab:</div>
                    <div class="parameter-value">0.00 kV</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('bus-voltage-ubc')">
                  <div class="parameter-content">
                    <div class="parameter-label">母线电压Ubc:</div>
                    <div class="parameter-value">0.00 kV</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('bus-voltage-uca')">
                  <div class="parameter-content">
                    <div class="parameter-label">母线电压Uca:</div>
                    <div class="parameter-value">0.00 kV</div>
                  </div>
                </div>



                <div class="parameter-item" style="display: none;" onclick="showParameterDetails('svg-current-ia')">
                  <div class="parameter-content">
                    <div class="parameter-label">SVG电流Ia:</div>
                    <div class="parameter-value">0.00 A</div>
                  </div>
                </div>

                <div class="parameter-item" style="display: none;" onclick="showParameterDetails('svg-current-ib')">
                  <div class="parameter-content">
                    <div class="parameter-label">SVG电流Ib:</div>
                    <div class="parameter-value">0.00 A</div>
                  </div>
                </div>

                <div class="parameter-item" style="display: none;" onclick="showParameterDetails('svg-current-ic')">
                  <div class="parameter-content">
                    <div class="parameter-label">SVG电流Ic:</div>
                    <div class="parameter-value">0.00 A</div>
                  </div>
                </div>

                <div class="parameter-item" onclick="showParameterDetails('grid-reactive-current')">
                  <div class="parameter-content">
                    <div class="parameter-label">网侧负载无功电流:</div>
                    <div class="parameter-value">0.00 A</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 水冷系统区域 - 占用50%高度 -->
          <div class="cooling-system-area">
            <div class="panel-header">
              <h3><i class="fas fa-tint"></i>水冷系统</h3>
            </div>

            <!-- 水冷系统运行状态 -->
            <div class="cooling-status-section">
              <div class="cooling-status-grid">
                <div class="cooling-status-item active">
                  <div class="status-indicator running">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">自动</span>
                </div>
                <div class="cooling-status-item">
                  <div class="status-indicator fault">
                    <i class="fas fa-circle"></i>
                  </div>
                  <span class="status-label">停止</span>
                </div>
              </div>
            </div>

            <!-- 水冷关键参数 - 多列网格布局 -->
            <div class="cooling-parameters">
              <h4><i class="fas fa-sliders-h"></i>关键参数</h4>
              <div class="parameter-grid-compact">
                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-cog"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">运行状态</div>
                    <div class="param-value">正常</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-stop-circle"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">远方停止信号</div>
                    <div class="param-value">无效</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-satellite-dish"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">远程模式</div>
                    <div class="param-value">启用</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-fan"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">风机机组</div>
                    <div class="param-value">运行</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-home"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">本地模式</div>
                    <div class="param-value">禁用</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-sync-alt"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">循环泵状态</div>
                    <div class="param-value">运行</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-robot"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">自动模式</div>
                    <div class="param-value">启用</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-tint"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">补水泵状态</div>
                    <div class="param-value">待机</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-hand-paper"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">手动模式</div>
                    <div class="param-value">禁用</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-fire"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">电加热状态</div>
                    <div class="param-value">关闭</div>
                  </div>
                </div>

                <div class="cooling-param-card" onclick="showCoolingDetails()">
                  <div class="param-icon">
                    <i class="fas fa-play"></i>
                  </div>
                  <div class="param-content">
                    <div class="param-label">远方启动信号</div>
                    <div class="param-value">有效</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </aside>

        <!-- 区域5：中栏 - 3D模型与故障信息 -->
        <section class="center-panel">
          <!-- Unity WebGL 界面切换容器 -->
          <div class="unity-wrapper" id="unityWrapper">
            <!-- 左侧箭头按钮 - 切换到上一个页面 -->
            <button class="arrow-btn left" id="prev-arrow" onclick="switchToPreviousView()" title="切换到上一个页面">
              <i class="fas fa-chevron-left"></i>
            </button>

            <!-- 右侧箭头按钮 - 切换到下一个页面 -->
            <button class="arrow-btn right" id="next-arrow" onclick="switchToNextView()" title="切换到下一个页面">
              <i class="fas fa-chevron-right"></i>
            </button>

            <!-- Unity 3D容器 -->
            <div class="unity-container show-unity" id="unityContainer">
              <iframe id="unity-iframe" src="index.html" frameborder="0" class="unity-iframe"></iframe>
              <div class="unity-placeholder" id="unityPlaceholder" style="display: none;">
                <div class="placeholder-content">
                  <i class="fas fa-cube rotating"></i>
                  <h3>3D模型加载中...</h3>
                  <p>正在初始化Unity WebGL引擎</p>
                  <div class="loading-bar">
                    <div class="loading-progress" id="loadingProgress"></div>
                  </div>
                </div>
              </div>

              <!-- 3D场景控制工具栏 - 移动到Unity容器内部 -->
              <div class="scene-toolbar">
                <div class="toolbar-group">
                  <button class="toolbar-btn" id="reset-view-btn" title="重置视角" onclick="resetUnityView()">
                    <i class="fas fa-home"></i>
                    <span></span>
                  </button>
                </div>
              </div>
            </div>

            <!-- 电气拓扑容器 -->
            <div class="topology-container electrical-topology-container" id="electricalTopologyContainer">
              <iframe id="electrical-topology-iframe" src="" frameborder="0" class="topology-iframe"></iframe>
            </div>

            <!-- 水冷拓扑容器 -->
            <div class="topology-container cooling-topology-container" id="coolingTopologyContainer">
              <iframe id="cooling-topology-iframe" src="" frameborder="0" class="topology-iframe"></iframe>
            </div>

            <!-- 加载动画覆盖层 -->
            <div class="loading-overlay" id="loadingOverlay">
              <div class="loading-spinner"></div>
              <div class="loading-text">正在加载页面...</div>
            </div>
          </div>

          <!-- 实时报警监控展示 -->
          <div class="alarm-monitor-section">
            <div class="alarm-header">
              <h4><i class="fas fa-bell"></i>实时报警监控</h4>
              <div class="alarm-filters">
                <button class="filter-btn active" data-filter="all">所有事件</button>
                <button class="filter-btn" data-filter="realtime">实时记录</button>
                <button class="filter-btn" data-filter="alarm">报警事件</button>
                <button class="filter-btn" data-filter="fault">故障事件</button>
              </div>
            </div>
            <div class="alarm-list" id="alarmList">
              <!-- 报警监控信息将通过JavaScript动态生成 -->
            </div>
          </div>
        </section>

        <!-- 区域6：右栏 - SVG电流曲线与相关链接 -->
        <aside class="right-panel">
          <div class="panel-header">
            <h3><i class="fas fa-chart-line"></i>SVG电流曲线</h3>
          </div>

          <!-- SVG电流Ia -->
          <div class="svg-current-section">
            <h4><i class="fas fa-bolt"></i>SVG电流</h4>
            <div class="current-curve-container" id="svg-current-ia">
              <div class="current-curve-header">Ia</div>
              <div class="current-curve-display">
                <canvas id="current-curve-ia" width="200" height="80"></canvas>
              </div>
            </div>
          </div>

          <!-- SVG电流Ib -->
          <div class="svg-current-section">
            <div class="current-curve-container" id="svg-current-ib">
              <div class="current-curve-header">Ib</div>
              <div class="current-curve-display">
                <canvas id="current-curve-ib" width="200" height="80"></canvas>
              </div>
            </div>
          </div>

          <!-- SVG电流Ic -->
          <div class="svg-current-section">
            <div class="current-curve-container" id="svg-current-ic">
              <div class="current-curve-header">Ic</div>
              <div class="current-curve-display">
                <canvas id="current-curve-ic" width="200" height="80"></canvas>
              </div>
            </div>
          </div>

          <!-- 相关链接区域 -->
          <div class="topology-links-section">
            <div class="section-header">
              <h4><i class="fas fa-external-link-alt"></i>相关链接</h4>
            </div>
            <div class="topology-links-grid">
              <button class="topology-link-btn" onclick="openSystemTopology()">
                <i class="fas fa-project-diagram"></i>
                <span>电气拓扑</span>
              </button>
              <button class="topology-link-btn" onclick="openCoolingTopology()">
                <i class="fas fa-tint"></i>
                <span>水冷拓扑</span>
              </button>
              <button class="topology-link-btn" onclick="openIOStatus()">
                <i class="fas fa-plug"></i>
                <span>I/O状态</span>
              </button>
              <button class="topology-link-btn" onclick="openUnitStatus()">
                <i class="fas fa-microchip"></i>
                <span>单元状态</span>
              </button>
              <button class="topology-link-btn" onclick="openMasterControl()">
                <i class="fas fa-sitemap"></i>
                <span>主控|辅控</span>
              </button>
            </div>
          </div>
        </aside>
      </main>

      <!-- 底部状态栏 -->
      <footer class="footer">
        <div class="footer-left">
          <div class="system-info">
            <span>系统版本: v2.1.0</span>
            <span>|</span>
            <span>最后更新: 2025-06-24 14:30:25</span>
          </div>
        </div>
        <div class="footer-center">
          <!-- 设备总数显示已移除 -->
        </div>
        <div class="footer-right">
          <div class="copyright">
            © 2025 桂林智源 - SVG 数字化系统
          </div>
        </div>
      </footer>
    </div>

    <!-- I/O状态弹窗 -->
    <div id="ioStatusModal" class="modal-overlay" style="display: none;">
      <div class="modal-container io-status-modal">
        <div class="modal-header">
          <h2><i class="fas fa-plug"></i>I/O状态监控</h2>
          <button class="modal-close-btn" onclick="closeIOStatusModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="io-status-grid">
            <!-- 输入IO状态 - 左侧 -->
            <div class="io-section input-section">
              <div class="io-section-header">
                <h3><i class="fas fa-sign-in-alt"></i>输入IO状态</h3>
              </div>
              <div class="io-columns">
                <!-- 第一列 -->
                <div class="io-column">
                  <div class="io-item">
                    <span class="io-label">启动按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">停止按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">复位按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">备用按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">急停按钮</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">SVG断路器</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">启动接触器</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">功率相序故障间</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">功率相序地刀间</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">远方/就地</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">外部故障</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统电源故障</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统预警</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统停水故障</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统停止</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                </div>
                <!-- 第二列 -->
                <div class="io-column">
                  <div class="io-item">
                    <span class="io-label">母联1状态输入</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">母联2状态输入</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">风机运行状态输入</span>
                    <div class="io-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">IB04备用</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入21</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入22</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入23</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入24</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入25</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入26</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入27</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入28</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入29</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入30</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入31</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输入32</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 输出IO状态 - 右侧 -->
            <div class="io-section output-section">
              <div class="io-section-header">
                <h3><i class="fas fa-sign-out-alt"></i>输出IO状态</h3>
              </div>
              <div class="io-columns">
                <!-- 第一列 -->
                <div class="io-column">
                  <div class="io-item">
                    <span class="io-label">SVG断路器分合闸命令</span>
                    <div class="io-indicator active">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">SVG断路器分闸命令</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">SVG断路器分闸命令</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">启动接触器合闸命令</span>
                    <div class="io-indicator active">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">启动接触器分闸命令</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统启动命令</span>
                    <div class="io-indicator active">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">水冷系统停止命令</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出09</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">连跳电容器柜</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">数据指示灯</span>
                    <div class="io-indicator active">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">报警指示灯</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">故障指示灯</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">开放指示灯</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">备用指示灯</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">SVG故障</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                </div>
                <!-- 第二列 -->
                <div class="io-column">
                  <div class="io-item">
                    <span class="io-label">输出17</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出18</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出19</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出20</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出21</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出22</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出23</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出24</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出25</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出26</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出27</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出28</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出29</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出30</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出31</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                  <div class="io-item">
                    <span class="io-label">输出32</span>
                    <div class="io-indicator inactive">
                      <i class="fas fa-square"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 拓扑图弹窗 -->
    <div id="topologyModal" class="modal-overlay" style="display: none;">
      <div class="modal-container topology-modal">
        <div class="modal-header">
          <h2 id="topologyModalTitle"><i class="fas fa-project-diagram"></i>系统拓扑图</h2>
          <button class="modal-close-btn" onclick="closeTopologyModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="topology-image-container">
            <img id="topologyImage" src="" alt="系统拓扑图" class="topology-image">
          </div>
          <div class="topology-iframe-container" id="topologyIframeContainer" style="display: none;">
            <iframe id="topologyIframe" src="" frameborder="0" class="topology-iframe"></iframe>
          </div>
        </div>
      </div>
    </div>

    <!-- 单元状态弹窗 -->
    <div id="unitStatusModal" class="modal-overlay" style="display: none;">
      <div class="modal-container unit-status-modal">
        <div class="modal-header">
          <h2><i class="fas fa-microchip"></i>单元状态监控</h2>
          <button class="modal-close-btn" onclick="closeUnitStatusModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="unit-status-layout">
            <!-- 左侧单元列表 -->
            <div class="unit-list-section">
              <div class="unit-list-header">
                <h3>功能单元列表</h3>
              </div>
              <div class="unit-list">
                <div class="unit-item active" data-unit="A01">
                  <span class="unit-id">A01</span>
                  <span class="unit-voltage">740</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A02">
                  <span class="unit-id">A02</span>
                  <span class="unit-voltage">740</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A03">
                  <span class="unit-id">A03</span>
                  <span class="unit-voltage">738</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A04">
                  <span class="unit-id">A04</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A05">
                  <span class="unit-id">A05</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A06">
                  <span class="unit-id">A06</span>
                  <span class="unit-voltage">740</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A07">
                  <span class="unit-id">A07</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A08">
                  <span class="unit-id">A08</span>
                  <span class="unit-voltage">740</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A09">
                  <span class="unit-id">A09</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A10">
                  <span class="unit-id">A10</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item" data-unit="A11">
                  <span class="unit-id">A11</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot inactive"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
                <div class="unit-item selected" data-unit="A12">
                  <span class="unit-id">A12</span>
                  <span class="unit-voltage">741</span>
                  <div class="unit-indicators">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot active"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 中间详细状态 -->
            <div class="unit-detail-section">
              <div class="unit-detail-header">
                <h3 id="selectedUnitTitle">A12 单元详细状态</h3>
                <button class="close-detail-btn" onclick="closeUnitDetail()">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="unit-detail-content">
                <div class="status-grid">
                  <div class="status-item">
                    <span class="status-label">下行光纤断</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">备用</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">封锁</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">超温</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">停止</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">欠压</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">启动</span>
                    <div class="status-indicator active">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过压</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">电源故障</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过流4</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">上行光纤断</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过流3</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">下行光纤去同步</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过流2</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">下行光纤无光</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                  <div class="status-item">
                    <span class="status-label">过流1</span>
                    <div class="status-indicator inactive">
                      <i class="fas fa-circle"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧状态统计 -->
            <div class="unit-stats-section">
              <div class="stats-header">
                <h3>状态统计</h3>
                <div class="voltage-display">
                  <div class="voltage-item">
                    <span class="voltage-label">A相 电压最大</span>
                    <span class="voltage-value">741 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">电压最小</span>
                    <span class="voltage-value">738 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">B相 电压最大</span>
                    <span class="voltage-value">742 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">电压最小</span>
                    <span class="voltage-value">739 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">C相 电压最大</span>
                    <span class="voltage-value">743 V</span>
                  </div>
                  <div class="voltage-item">
                    <span class="voltage-label">电压最小</span>
                    <span class="voltage-value">738 V</span>
                  </div>
                </div>
              </div>
              <div class="unit-grid-display">
                <div class="grid-section">
                  <h4>B组单元</h4>
                  <div class="unit-mini-grid">
                    <div class="mini-unit" data-unit="B11">B11<br>740</div>
                    <div class="mini-unit" data-unit="B12">B12<br>742</div>
                  </div>
                </div>
                <div class="grid-section">
                  <h4>C组单元</h4>
                  <div class="unit-mini-grid">
                    <div class="mini-unit" data-unit="C11">C11<br>742</div>
                    <div class="mini-unit" data-unit="C12">C12<br>742</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模块弹窗 -->
    <div id="moduleModal" class="modal-overlay" style="display: none;">
      <div class="modal-container module-modal">
        <div class="modal-header">
          <h2 id="moduleModalTitle"><i class="fas fa-cube"></i>模块</h2>
          <button class="modal-close-btn" onclick="closeModuleModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="module-iframe-container">
            <iframe id="moduleIframe" src="" frameborder="0" class="module-iframe"></iframe>
          </div>
        </div>
      </div>
    </div>

    <script>
      /**
       * 桂林智源 SVG 数字化系统 - 主页面脚本
       * 包含Unity WebGL界面切换功能
       */

      // ==================== Unity WebGL 界面切换功能 ====================

      // 全局变量 - 循环队列切换机制
      const viewQueue = [
        {
          id: 'unity',
          name: 'Unity 3D',
          displayName: '3D界面',
          containerClass: 'show-unity',
          iframeSrc: '',
          isUnity: true
        },
        {
          id: 'electrical-topology',
          name: '电气拓扑',
          displayName: '电气拓扑页面',
          containerClass: 'show-electrical-topology',
          iframeSrc: '电气拓扑.html',
          isUnity: false
        },
        {
          id: 'cooling-topology',
          name: '水冷拓扑',
          displayName: '水冷拓扑页面',
          containerClass: 'show-cooling-topology',
          iframeSrc: '水冷拓扑.html',
          isUnity: false
        }
      ];

      let currentViewIndex = 0; // 当前页面在队列中的索引
      let loadedPages = new Set(); // 记录已加载的页面

      /**
       * 初始化Unity WebGL界面切换功能 - 循环队列机制
       */
      function initUnityViewSwitcher() {
        console.log('初始化Unity WebGL界面切换功能（循环队列机制）...');

        // 设置初始状态
        switchToView(0, false, true); // 切换到第一个页面（Unity 3D），不显示加载动画，强制更新

        // 预加载所有页面
        preloadAllPages();

        // 设置键盘快捷键
        document.addEventListener('keydown', function(e) {
          if (e.ctrlKey && e.key === 'ArrowLeft') {
            e.preventDefault();
            switchToPreviousView();
          } else if (e.ctrlKey && e.key === 'ArrowRight') {
            e.preventDefault();
            switchToNextView();
          } else if (e.ctrlKey && e.key === '1') {
            e.preventDefault();
            switchToViewById('unity');
          } else if (e.ctrlKey && e.key === '2') {
            e.preventDefault();
            switchToViewById('electrical-topology');
          } else if (e.ctrlKey && e.key === '3') {
            e.preventDefault();
            switchToViewById('cooling-topology');
          }
        });

        console.log('Unity WebGL界面切换功能初始化完成');
      }

      /**
       * 预加载所有页面
       */
      function preloadAllPages() {
        viewQueue.forEach(view => {
          if (!view.isUnity && view.iframeSrc && !loadedPages.has(view.id)) {
            preloadPage(view);
          }
        });
      }

      /**
       * 预加载指定页面
       */
      function preloadPage(view) {
        const iframeId = `${view.id}-iframe`;
        const iframe = document.getElementById(iframeId);

        if (iframe && !loadedPages.has(view.id)) {
          iframe.src = view.iframeSrc;

          iframe.onload = function() {
            loadedPages.add(view.id);
            console.log(`${view.displayName}预加载完成`);
          };

          iframe.onerror = function() {
            console.error(`${view.displayName}加载失败`);
          };
        }
      }

      /**
       * 切换到上一个页面
       */
      function switchToPreviousView() {
        const prevIndex = (currentViewIndex - 1 + viewQueue.length) % viewQueue.length;
        switchToView(prevIndex);
      }

      /**
       * 切换到下一个页面
       */
      function switchToNextView() {
        const nextIndex = (currentViewIndex + 1) % viewQueue.length;
        switchToView(nextIndex);
      }

      /**
       * 根据页面ID切换到指定页面
       */
      function switchToViewById(viewId) {
        const index = viewQueue.findIndex(view => view.id === viewId);
        if (index !== -1) {
          switchToView(index);
        }
      }

      /**
       * 切换到指定索引的页面
       */
      function switchToView(index, showLoading = true, forceUpdate = false) {
        // 如果不是强制更新且索引相同，则跳过
        if (!forceUpdate && index === currentViewIndex) return;

        const targetView = viewQueue[index];
        if (!targetView) return;

        console.log(`切换到${targetView.displayName}`);

        if (showLoading) {
          showLoadingOverlay(`正在切换到${targetView.displayName}...`);
        }

        // 确保非Unity页面已加载
        if (!targetView.isUnity && !loadedPages.has(targetView.id)) {
          preloadPage(targetView);
        }

        const delay = showLoading ? 300 : 0;
        setTimeout(() => {
          const wrapper = document.getElementById('unityWrapper');
          if (wrapper) {
            wrapper.className = `unity-wrapper ${targetView.containerClass}`;
          }

          // 更新当前索引
          currentViewIndex = index;

          // 更新按钮状态
          updateArrowButtonStates();

          if (showLoading) {
            hideLoadingOverlay();
          }
        }, delay);
      }

      /**
       * 更新箭头按钮状态 - 循环队列机制
       */
      function updateArrowButtonStates() {
        const prevArrow = document.getElementById('prev-arrow');
        const nextArrow = document.getElementById('next-arrow');

        if (!prevArrow || !nextArrow) return;

        const currentView = viewQueue[currentViewIndex];
        const prevIndex = (currentViewIndex - 1 + viewQueue.length) % viewQueue.length;
        const nextIndex = (currentViewIndex + 1) % viewQueue.length;
        const prevView = viewQueue[prevIndex];
        const nextView = viewQueue[nextIndex];

        // 重置按钮状态
        prevArrow.classList.remove('current-view');
        nextArrow.classList.remove('current-view');

        // 设置当前视图指示（左箭头表示当前视图）
        // prevArrow.classList.add('current-view');

        // 更新工具提示
        // prevArrow.title = `当前：${currentView.displayName} | 上一个：${prevView.name} (Ctrl+←)`;
        // nextArrow.title = `下一个：${nextView.name} (Ctrl+→)`;
        prevArrow.title = `Ctrl+←`;
        nextArrow.title = `Ctrl+→`;

        console.log(`当前页面：${currentView.displayName} (${currentViewIndex + 1}/${viewQueue.length})`);
      }

      /**
       * 显示加载覆盖层
       * @param {string} text - 加载文本
       */
      function showLoadingOverlay(text = '正在加载...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
          const loadingText = overlay.querySelector('.loading-text');
          if (loadingText) {
            loadingText.textContent = text;
          }
          overlay.classList.add('show');
        }
      }

      /**
       * 隐藏加载覆盖层
       */
      function hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
          overlay.classList.remove('show');
        }
      }

      // ==================== 兼容性函数 ====================

      /**
       * 兼容性函数：切换到Unity 3D界面
       */
      function switchToUnity() {
        switchToViewById('unity');
      }

      /**
       * 兼容性函数：切换到电气拓扑页面
       */
      function switchToTopology() {
        switchToViewById('electrical-topology');
      }

      // ==================== 原有功能代码 ====================
      /**
       * 处理Unity WebGL嵌入、参数监控和故障信息显示
       */

      var unityInstance = null;
      var unityIframe = null;

      // 页面加载完成后初始化
      window.addEventListener("load", function () {
        initMainPage();
        updateTime();
        setInterval(updateTime, 1000);
        // 初始化Unity WebGL界面切换功能
        initUnityViewSwitcher();
        // 初始化报警监控筛选
        initAlarmFilters();
        // 模拟数据更新
        setInterval(updateSystemData, 5000);
        // 模拟状态变化（仅用于演示）
        setTimeout(() => {
          simulateStatusChange();
        }, 10000);
        // 初始化报警监控并开始模拟
        initializeAlarmLog();
        startAlarmSimulation();
        // 初始化SVG电流曲线
        setTimeout(() => {
          console.log('开始初始化SVG电流曲线...');
          initCurrentCurves();
        }, 1500);

        // 初始化弹窗事件监听
        initModalEvents();
      });

      /**
       * 更新时间显示
       */
      function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
          timeElement.textContent = timeString;
        }
      }

      /**
       * 初始化主页面
       */
      function initMainPage() {
        unityIframe = document.getElementById('unity-iframe');

        // 监听来自Unity iframe的消息
        window.addEventListener('message', function(event) {
          // 确保消息来源安全
          if (event.source !== unityIframe.contentWindow) {
            return;
          }

          if (event.data && event.data.type === 'unityLoaded') {
            console.log('Unity WebGL 加载完成');
            onUnityLoaded();
          }
        });

        // 绑定控制按钮事件
        bindControlEvents();

        // 绑定窗口大小变化事件
        window.addEventListener('resize', function() {
          resizeAllCharts();
        });

        // 添加按钮点击波纹效果
        addRippleEffect();
      }

      /**
       * Unity加载完成后的回调
       */
      function onUnityLoaded() {
        // 初始化SVG电流曲线
        initCurrentCurves();
      }

      /**
       * 初始化SVG电流曲线显示
       */
      function initCurrentCurves() {
        console.log('初始化SVG电流曲线显示');

        // 初始化三个电流曲线
        initCurrentCurve('current-curve-ia', '#ff4444'); // 红色 - Ia
        initCurrentCurve('current-curve-ib', '#44ff44'); // 绿色 - Ib
        initCurrentCurve('current-curve-ic', '#4444ff'); // 蓝色 - Ic

        // 开始实时更新曲线
        startCurrentCurveAnimation();
      }

      /**
       * 初始化单个电流曲线
       * @param {string} canvasId - Canvas元素ID
       * @param {string} color - 曲线颜色
       */
      function initCurrentCurve(canvasId, color) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
          console.error('Canvas元素未找到:', canvasId);
          return;
        }

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        // 设置样式
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';

        // 绘制网格
        drawGrid(ctx, width, height);

        // 存储画布上下文以便后续更新
        canvas.ctx = ctx;
        canvas.color = color;
        canvas.dataPoints = [];
        canvas.animationPhase = Math.random() * Math.PI * 2; // 随机相位
      }

      /**
       * 绘制网格背景
       * @param {CanvasRenderingContext2D} ctx - 画布上下文
       * @param {number} width - 画布宽度
       * @param {number} height - 画布高度
       */
      function drawGrid(ctx, width, height) {
        ctx.strokeStyle = 'rgba(0, 212, 255, 0.2)';
        ctx.lineWidth = 0.5;

        // 绘制水平网格线
        for (let y = 0; y <= height; y += height / 4) {
          ctx.beginPath();
          ctx.moveTo(0, y);
          ctx.lineTo(width, y);
          ctx.stroke();
        }

        // 绘制垂直网格线
        for (let x = 0; x <= width; x += width / 8) {
          ctx.beginPath();
          ctx.moveTo(x, 0);
          ctx.lineTo(x, height);
          ctx.stroke();
        }
      }

      /**
       * 开始电流曲线动画
       */
      function startCurrentCurveAnimation() {
        setInterval(() => {
          updateCurrentCurve('current-curve-ia');
          updateCurrentCurve('current-curve-ib');
          updateCurrentCurve('current-curve-ic');
        }, 100); // 每100ms更新一次
      }

      /**
       * 更新单个电流曲线
       * @param {string} canvasId - Canvas元素ID
       */
      function updateCurrentCurve(canvasId) {
        const canvas = document.getElementById(canvasId);
        if (!canvas || !canvas.ctx) return;

        const ctx = canvas.ctx;
        const width = canvas.width;
        const height = canvas.height;
        const color = canvas.color;

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        // 重新绘制网格
        drawGrid(ctx, width, height);

        // 更新相位
        canvas.animationPhase += 0.2;

        // 绘制正弦波
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.beginPath();

        const amplitude = height * 0.3; // 振幅
        const centerY = height / 2; // 中心线
        const frequency = 2; // 频率

        for (let x = 0; x < width; x++) {
          const angle = (x / width) * Math.PI * 2 * frequency + canvas.animationPhase;
          const y = centerY + Math.sin(angle) * amplitude;

          if (x === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }

        ctx.stroke();

        // 添加发光效果
        ctx.shadowColor = color;
        ctx.shadowBlur = 5;
        ctx.stroke();
        ctx.shadowBlur = 0;
      }

      /**
       * 绑定控制按钮事件
       */
      function bindControlEvents() {
        // 绑定图表控制按钮事件
        bindChartControlEvents();

        // 绑定重置视角按钮事件（如果存在）
        const resetViewBtn = document.getElementById("reset-view-btn");
        if (resetViewBtn) {
          resetViewBtn.addEventListener("click", function() {
            resetUnityView();
          });
        }
      }



      /**
       * 绑定图表控制按钮事件
       */
      function bindChartControlEvents() {
        const chartBtns = document.querySelectorAll('.chart-btn');
        chartBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            // 移除其他按钮的active状态
            const parentControls = this.parentElement;
            parentControls.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');
          });
        });
      }

      /**
       * 向Unity发送命令
       * @param {string} target - 目标GameObject名称
       * @param {string} method - 要调用的方法名
       * @param {string} parameter - 可选参数
       */
      function sendUnityCommand(target, method, parameter) {
        try {
          if (unityIframe && unityIframe.contentWindow) {
            unityIframe.contentWindow.postMessage({
              type: 'unityCommand',
              target: target,
              method: method,
              parameter: parameter || ''
            }, '*');
            console.log('发送Unity命令:', target, method, parameter);
          } else {
            console.warn('Unity iframe 未准备就绪');
          }
        } catch (error) {
          console.error('发送Unity命令失败:', error);
        }
      }

      /**
       * 添加按钮点击波纹效果
       */
      function addRippleEffect() {
        const buttons = document.querySelectorAll('.control-btn, .toolbar-btn, .chart-btn');
        buttons.forEach(button => {
          button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
              ripple.remove();
            }, 600);
          });
        });
      }

      /**
       * 初始化报警监控筛选功能
       */
      function initAlarmFilters() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            // 移除其他按钮的active状态
            filterBtns.forEach(b => b.classList.remove('active'));
            // 添加当前按钮的active状态
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');
            filterAlarmList(filter);
          });
        });
      }

      /**
       * 筛选报警监控列表
       * @param {string} filter - 筛选类型：all, realtime, alarm, fault
       */
      function filterAlarmList(filter) {
        const alarmItems = document.querySelectorAll('.alarm-item');
        alarmItems.forEach(item => {
          switch(filter) {
            case 'all':
              item.style.display = 'flex';
              break;
            case 'realtime':
              // 显示最近的记录
              item.style.display = 'flex';
              break;
            case 'alarm':
              // 只显示报警事件
              if (item.classList.contains('alarm')) {
                item.style.display = 'flex';
              } else {
                item.style.display = 'none';
              }
              break;
            case 'fault':
              // 只显示故障事件
              if (item.classList.contains('fault')) {
                item.style.display = 'flex';
              } else {
                item.style.display = 'none';
              }
              break;
          }
        });
      }

      /**
       * 显示参数详情页面
       * @param {string} paramType - 参数类型
       */
      function showParameterDetails(paramType) {
        console.log('显示参数详情:', paramType);

        const paramNames = {
          'current': 'SVG总电流',
          'voltage': 'SVG总电压',
          'power': '功率因数',
          'frequency': '系统频率',
          'temperature': '设备温度',
          'efficiency': '运行效率'
        };

        const paramName = paramNames[paramType] || paramType;
        alert(`即将显示${paramName}参数的详细趋势曲线与历史数据`);
      }

      /**
       * 显示水冷系统详情
       */
      function showCoolingDetails() {
        console.log('显示水冷系统详情');
        alert('即将显示水冷系统详情页，查看实时数据曲线');
      }

      /**
       * 更新系统状态显示
       * @param {string} status - 状态：ready, fault, charging, waiting, running
       */
      function updateSystemStatus(status) {
        // 移除所有状态项的active类
        const statusItems = document.querySelectorAll('.status-item');
        statusItems.forEach(item => item.classList.remove('active'));

        // 为指定状态添加active类
        const targetItem = document.querySelector(`[data-status="${status}"]`);
        if (targetItem) {
          targetItem.classList.add('active');
        }
      }

      /**
       * 模拟状态变化（仅用于演示）
       */
      function simulateStatusChange() {
        const statuses = ['ready', 'charging', 'waiting', 'running'];
        let currentIndex = 3; // 从运行状态开始

        setInterval(() => {
          currentIndex = (currentIndex + 1) % statuses.length;
          updateSystemStatus(statuses[currentIndex]);

          // 添加状态变化日志到报警监控
          addAlarmLog('recovery', '系统控制', `系统状态切换为: ${getStatusName(statuses[currentIndex])}`);
        }, 15000); // 每15秒切换一次状态
      }

      /**
       * 获取状态中文名称
       */
      function getStatusName(status) {
        const statusNames = {
          'ready': '就绪',
          'fault': '故障',
          'charging': '充电',
          'waiting': '合高压等待',
          'running': '运行'
        };
        return statusNames[status] || status;
      }

      /**
       * 添加报警监控日志
       * @param {string} type - 事件类型：alarm(报警), fault(故障), recovery(恢复)
       * @param {string} device - 设备名称
       * @param {string} message - 事件描述
       * @param {Date} timestamp - 时间戳（可选）
       */
      function addAlarmLog(type, device, message, timestamp = null) {
        const alarmList = document.querySelector('.alarm-list');
        if (alarmList) {
          // 使用传入的时间戳或当前时间
          const now = timestamp ? new Date(timestamp) : new Date();

          // 手动格式化时间，避免Invalid Date问题
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const hours = String(now.getHours()).padStart(2, '0');
          const minutes = String(now.getMinutes()).padStart(2, '0');
          const seconds = String(now.getSeconds()).padStart(2, '0');

          const dateStr = `${year}-${month}-${day}`;
          const timeStr = `${hours}:${minutes}:${seconds}`;

          const newItem = document.createElement('div');
          newItem.className = `alarm-item ${type}`;
          newItem.dataset.timestamp = now.getTime(); // 存储时间戳用于排序
          newItem.innerHTML = `
            <span class="alarm-serial">0</span>
            <span class="alarm-date">${dateStr}</span>
            <span class="alarm-time">${timeStr}</span>
            <span class="alarm-device">${device}</span>
            <span class="alarm-message">${message}</span>
          `;

          // 将新项目添加到数组中并按时间排序
          const allItems = Array.from(alarmList.querySelectorAll('.alarm-item'));
          allItems.push(newItem);

          // 按时间戳降序排序（最新的在前）
          allItems.sort((a, b) => {
            const timeA = parseInt(a.dataset.timestamp) || 0;
            const timeB = parseInt(b.dataset.timestamp) || 0;
            return timeB - timeA;
          });

          // 清空列表并重新添加排序后的项目，同时更新序号
          alarmList.innerHTML = '';
          allItems.forEach((item, index) => {
            // 更新序号（从1开始，最新的事件序号最大）
            const serialSpan = item.querySelector('.alarm-serial');
            if (serialSpan) {
              serialSpan.textContent = String(allItems.length - index);
            }
            alarmList.appendChild(item);
          });

          // 限制显示最多15条记录
          const finalItems = alarmList.querySelectorAll('.alarm-item');
          if (finalItems.length > 15) {
            for (let i = 15; i < finalItems.length; i++) {
              finalItems[i].remove();
            }
            // 重新更新序号（因为删除了一些项目）
            updateAlarmSerialNumbers();
          }
        }
      }

      /**
       * 更新报警监控列表的序号
       */
      function updateAlarmSerialNumbers() {
        const alarmList = document.querySelector('.alarm-list');
        if (alarmList) {
          const items = alarmList.querySelectorAll('.alarm-item');
          items.forEach((item, index) => {
            const serialSpan = item.querySelector('.alarm-serial');
            if (serialSpan) {
              serialSpan.textContent = String(items.length - index);
            }
          });
        }
      }

      /**
       * 模拟系统数据更新
       */
      function updateSystemData() {
        // 更新电气系统参数
        const electricalParams = document.querySelectorAll('.system-status-area .parameter-item .parameter-value');
        if (electricalParams.length >= 13) {
          // 负载有功电流
          const loadActiveCurrent = (Math.random() * 10).toFixed(4);
          electricalParams[0].textContent = loadActiveCurrent + ' A';

          // 负载无功电流
          const loadReactiveCurrent = (Math.random() * 10).toFixed(4);
          electricalParams[1].textContent = loadReactiveCurrent + ' A';

          // SVG有功电流
          const svgActiveCurrent = (Math.random() * 10).toFixed(4);
          electricalParams[2].textContent = svgActiveCurrent + ' A';

          // SVG无功电流
          const svgReactiveCurrent = (Math.random() * 10).toFixed(4);
          electricalParams[3].textContent = svgReactiveCurrent + ' A';

          // 无功功率
          const reactivePower = (Math.random() * 100).toFixed(2);
          electricalParams[4].textContent = reactivePower + ' MVAr';

          // 功率因数
          const powerFactor = (0.85 + Math.random() * 0.15).toFixed(3);
          electricalParams[5].textContent = powerFactor;

          // 母线电压Uab
          const busVoltageUab = (10.0 + Math.random() * 0.5).toFixed(2);
          electricalParams[6].textContent = busVoltageUab + ' kV';

          // 母线电压Ubc
          const busVoltageUbc = (10.0 + Math.random() * 0.5).toFixed(2);
          electricalParams[7].textContent = busVoltageUbc + ' kV';

          // 母线电压Uca
          const busVoltageUca = (10.0 + Math.random() * 0.5).toFixed(2);
          electricalParams[8].textContent = busVoltageUca + ' kV';

          // SVG电流Ia
          const svgCurrentIa = (Math.random() * 50).toFixed(2);
          electricalParams[9].textContent = svgCurrentIa + ' A';

          // SVG电流Ib
          const svgCurrentIb = (Math.random() * 50).toFixed(2);
          electricalParams[10].textContent = svgCurrentIb + ' A';

          // SVG电流Ic
          const svgCurrentIc = (Math.random() * 50).toFixed(2);
          electricalParams[11].textContent = svgCurrentIc + ' A';

          // 网侧负载无功电流
          const gridReactiveCurrent = (Math.random() * 20).toFixed(2);
          electricalParams[12].textContent = gridReactiveCurrent + ' A';
        }

        // 更新水冷系统参数
        updateCoolingParameters();
      }

      /**
       * 更新水冷系统参数
       */
      function updateCoolingParameters() {
        const coolingParams = document.querySelectorAll('.cooling-param-card .param-value');
        if (coolingParams.length >= 11) {
          // 运行状态
          const runningStatus = Math.random() > 0.05 ? '正常' : '异常';
          coolingParams[0].textContent = runningStatus;

          // 远方停止信号
          const remoteStopSignal = Math.random() > 0.9 ? '有效' : '无效';
          coolingParams[1].textContent = remoteStopSignal;

          // 远程模式
          const remoteMode = Math.random() > 0.2 ? '启用' : '禁用';
          coolingParams[2].textContent = remoteMode;

          // 风机机组
          const fanUnit = Math.random() > 0.1 ? '运行' : '停止';
          coolingParams[3].textContent = fanUnit;

          // 本地模式
          const localMode = Math.random() > 0.8 ? '启用' : '禁用';
          coolingParams[4].textContent = localMode;

          // 循环泵状态
          const circulationPump = Math.random() > 0.05 ? '运行' : '停止';
          coolingParams[5].textContent = circulationPump;

          // 自动模式
          const autoMode = Math.random() > 0.1 ? '启用' : '禁用';
          coolingParams[6].textContent = autoMode;

          // 补水泵状态
          const waterPump = Math.random() > 0.7 ? '运行' : '待机';
          coolingParams[7].textContent = waterPump;

          // 手动模式
          const manualMode = Math.random() > 0.9 ? '启用' : '禁用';
          coolingParams[8].textContent = manualMode;

          // 电加热状态
          const heaterStatus = Math.random() > 0.8 ? '开启' : '关闭';
          coolingParams[9].textContent = heaterStatus;

          // 远方启动信号
          const remoteStartSignal = Math.random() > 0.1 ? '有效' : '无效';
          coolingParams[10].textContent = remoteStartSignal;
        }
      }

      /**
       * 初始化报警监控日志
       */
      function initializeAlarmLog() {
        // 添加系统启动日志
        addAlarmLog('recovery', '系统控制', '系统启动完成，所有设备运行正常');

        // 添加一些历史记录（模拟过去的事件）
        const now = new Date();
        const pastEvents = [
          { type: 'recovery', device: 'SVG主控', message: 'SVG系统自检完成', offset: -180000 }, // 3分钟前
          { type: 'fault', device: 'IGBT模块', message: 'IGBT模块过热故障，已切换备用', offset: -360000 }, // 6分钟前
          { type: 'alarm', device: '水冷系统', message: '水冷系统温度略高，已自动调节', offset: -540000 }, // 9分钟前
          { type: 'fault', device: '通信模块', message: '通信链路中断，正在重连', offset: -720000 }, // 12分钟前
          { type: 'recovery', device: '电压调节', message: '电压参数调整完成', offset: -900000 }, // 15分钟前
          { type: 'alarm', device: '功率监测', message: '功率因数偏低，建议检查负载', offset: -1080000 }, // 18分钟前
          { type: 'fault', device: '电源模块', message: '电源模块异常，已启动保护', offset: -1260000 }, // 21分钟前
          { type: 'recovery', device: '系统控制', message: '系统故障恢复，运行正常', offset: -1440000 }, // 24分钟前
          { type: 'alarm', device: '直流母线', message: '直流母线电压波动', offset: -1620000 }, // 27分钟前
          { type: 'recovery', device: '通信模块', message: '通信链路检测正常', offset: -1800000 }, // 30分钟前
        ];

        pastEvents.forEach(event => {
          const eventTime = new Date(now.getTime() + event.offset);
          addAlarmLog(event.type, event.device, event.message, eventTime);
        });
      }

      /**
       * 开始报警监控模拟
       */
      function startAlarmSimulation() {
        // 定期生成随机事件
        setInterval(() => {
          generateRandomAlarmEvent();
        }, 5000); // 随机间隔
      }

      /**
       * 生成随机报警监控事件
       */
      function generateRandomAlarmEvent() {
        const eventTypes = [
          { type: 'recovery', weight: 50 },
          { type: 'alarm', weight: 30 },
          { type: 'fault', weight: 20 }
        ];

        // 设备列表
        const devices = [
          'SVG主控', 'IGBT模块', '水冷系统', '通信模块', '电压调节',
          '功率监测', '电源模块', '系统控制', '直流母线', '变压器',
          '电抗器', '电容器组', '风机系统', '温度监测', '保护装置'
        ];

        // 恢复正常事件
        const recoveryEvents = [
          '系统运行状态正常',
          'SVG模块参数调整完成',
          '水冷系统运行稳定',
          '电压电流参数正常',
          '功率因数调节完成',
          '通信状态良好',
          '设备温度正常',
          '系统自检通过',
          '负载平衡调整完成',
          '数据采集正常',
          '故障恢复，设备正常运行',
          '参数调整完成，系统稳定',
          '维护作业完成',
          '设备重启成功'
        ];

        // 报警事件
        const alarmEvents = [
          '水冷系统温度偏高',
          '功率因数略低',
          '电压波动检测',
          '通信延迟增加',
          '负载不平衡警告',
          '温度传感器异常',
          '电流谐波超标',
          '系统响应时间延长',
          '设备运行时间过长',
          '环境温度升高',
          '直流母线电压偏低',
          'IGBT温度接近上限',
          '冷却液流量不足',
          '电抗器温度偏高',
          '变压器负载率高',
          '电容器容量下降',
          '风机转速异常',
          '绝缘电阻偏低',
          '谐波含量超标',
          '功率输出不稳定',
          '控制精度下降',
          '响应速度变慢',
          '数据采集延迟',
          '备用设备预警',
          '维护周期临近'
        ];

        // 故障事件
        const faultEvents = [
          'SVG模块通信中断',
          '水冷系统严重故障',
          '电压保护动作',
          '过流保护触发',
          '温度保护启动',
          '通信链路完全中断',
          '传感器读取失败',
          '控制器响应超时',
          '电源模块异常',
          '系统紧急停机',
          'IGBT模块过热故障',
          '直流母线电压异常',
          '交流接触器故障',
          '冷却泵电机故障',
          '变压器绝缘故障',
          '电抗器过载保护',
          '控制电源故障',
          '光纤通信中断',
          '主控板硬件故障',
          '风机系统故障',
          '绝缘监测报警',
          '接地故障检测',
          '谐波滤波器故障',
          '电容器组故障',
          '断路器拒动故障',
          '保护装置异常',
          '测量回路断线',
          '操作回路故障',
          '辅助电源故障',
          '人机界面通信故障'
        ];

        // 根据权重随机选择事件类型
        const totalWeight = eventTypes.reduce((sum, type) => sum + type.weight, 0);
        let random = Math.random() * totalWeight;
        let selectedType = 'recovery';

        for (const eventType of eventTypes) {
          random -= eventType.weight;
          if (random <= 0) {
            selectedType = eventType.type;
            break;
          }
        }

        // 根据类型选择事件消息和设备
        let events;
        switch (selectedType) {
          case 'alarm':
            events = alarmEvents;
            break;
          case 'fault':
            events = faultEvents;
            break;
          default:
            events = recoveryEvents;
        }

        const randomDevice = devices[Math.floor(Math.random() * devices.length)];
        const randomMessage = events[Math.floor(Math.random() * events.length)];
        addAlarmLog(selectedType, randomDevice, randomMessage);
      }

      /**
       * 切换主菜单显示/隐藏
       */
      function toggleMainMenu() {
        const dropdown = document.getElementById('mainMenuDropdown');
        dropdown.classList.toggle('show');

        // 点击其他地方关闭菜单
        document.addEventListener('click', function(event) {
          if (!event.target.closest('.header-center')) {
            dropdown.classList.remove('show');
          }
        });
      }

      /**
       * 导航到指定模块
       * @param {string} module - 模块名称
       */
      function navigateToModule(module) {
        const dropdown = document.getElementById('mainMenuDropdown');
        dropdown.classList.remove('show');

        // 这里可以根据需要实现具体的导航逻辑
        console.log(`导航到模块: ${module}`);

        // 根据模块类型显示对应的弹窗
        switch(module) {
          case 'realtime-curve':
            showModuleModal('realtime-curve', '实时曲线', 'fas fa-chart-line', '实时数据.html');
            break;
          case 'history-record':
            showModuleModal('history-record', '历史记录', 'fas fa-history', '历史记录.html');
            break;
          case 'history-event':
            showModuleModal('history-event', '历史事件', 'fas fa-calendar-alt', '历史事件.html');
            break;
          case 'fault-wave':
            showModuleModal('fault-wave', '故障录波', 'fas fa-wave-square', '故障录波.html');
            break;
          case 'parameter-curve':
            showModuleModal('parameter-curve', '参数曲线', 'fas fa-chart-area', '参数曲线.html');
            break;
          case 'dsp':
            showModuleModal('dsp', 'DSP', 'fas fa-microchip', 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=721de54b-bed8-43dc-9157-81ac6cff32a4&type=3&date=' + new Date());
            break;
          case 'version-info':
            showModuleModal('version-info', '版本信息', 'fas fa-info-circle', 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=eb900ac1-737c-4610-b4b3-ea05239531e3&type=3&date=' + new Date());
            break;
          default:
            console.warn(`未知模块: ${module}`);
        }
      }

      /**
       * 获取模块中文名称
       * @param {string} module - 模块英文名称
       * @returns {string} 中文名称
       */
      function getModuleName(module) {
        const moduleNames = {
          'realtime-curve': '实时曲线',
          'history-record': '历史记录',
          'history-event': '历史事件',
          'fault-wave': '故障录波',
          'parameter-curve': '参数曲线',
          'version-info': '版本信息'
        };
        return moduleNames[module] || module;
      }

      /**
       * 打开水冷系统拓扑图
       */
      function openCoolingTopology() {
        console.log('打开水冷系统拓扑图');
        // showTopologyModal('cooling');
        let date = new Date();
        showModuleModal('cooling-topology', '水冷系统拓扑图', 'fas fa-tint', 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=bdd07113-f2fd-4744-88f0-a055916c976b&type=3&date=' + date);
      }

      /**
       * 打开SVG系统拓扑图
       */
      function openSystemTopology() {
        console.log('打开SVG系统拓扑图');
        // showTopologyModal('electrical');
        let date = new Date();
        showModuleModal('electrical-topology', '电气系统拓扑图', 'fas fa-bolt', 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=6f2b118e-1b27-4ebd-b1db-bcd08ce10bbf&type=3&date=' + date);
      }

      /**
       * 打开I/O状态页面
       */
      function openIOStatus() {
        console.log('打开I/O状态页面');
        // showIOStatusModal();
        let date = new Date();
        showModuleModal('io-status', 'I/O状态监控', 'fas fa-plug', 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=df533b38-98b3-4c1a-9a3a-b2d7884f7770&type=3&date=' + date);
      }

      /**
       * 打开单元状态页面
       */
      function openUnitStatus() {
        console.log('打开单元状态页面');
        // showUnitStatusModal();
        let date = new Date();
        showModuleModal('unit-status', '单元状态监控', 'fas fa-microchip', 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=f89049d1-5172-42ec-b9e9-540685800c7f&type=3&wework_cfm_code=NOhs%2BuVWHo97Du860XjXIPC5tyE8DwbeJo3xoLtc8tn94QGaXR9LJW5VvFSCVJID5Fpwj6f%2FVjFU6LVqrXItFhckh8qcSUUqInRO3%2Fb3FD0Ee6bfED2vOqLVG6i2ymNIFQ7%2Fi%2BDxy8I7Xi5S1uPRzyxBjWWrv5w9p218BH1F2vIV&date=' + date);
      }

      /**
       * 打开主控|辅控页面
       */
      function openMasterControl() {
        console.log('打开主控|辅控页面');
        let date = new Date();
        showModuleModal('master-control', '主控|辅控', 'fas fa-sitemap', 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=6f1379ce-d7b5-4017-9d9d-78d49813cd8c&type=3&date=' + date);
      }

      /**
       * 重置Unity视角
       */
      function resetUnityView() {
        sendUnityCommand("Main Camera", "SwitchToOverviewPosition");
        console.log('重置Unity视角');
      }

      /**
       * 显示I/O状态弹窗
       */
      function showIOStatusModal() {
        const modal = document.getElementById('ioStatusModal');
        if (modal) {
          modal.style.display = 'flex';
          // 添加显示动画
          setTimeout(() => {
            modal.classList.add('show');
          }, 10);
          // 模拟I/O状态更新
          simulateIOStatusUpdate();
        }
      }

      /**
       * 关闭I/O状态弹窗
       */
      function closeIOStatusModal() {
        const modal = document.getElementById('ioStatusModal');
        if (modal) {
          modal.classList.remove('show');
          setTimeout(() => {
            modal.style.display = 'none';
          }, 300);
        }
      }

      /**
       * 模拟I/O状态更新
       */
      function simulateIOStatusUpdate() {
        // 随机更新一些I/O状态
        const indicators = document.querySelectorAll('#ioStatusModal .io-indicator');

        // 定期更新状态
        const updateInterval = setInterval(() => {
          // 随机选择几个指示器进行状态切换
          const randomIndicators = Array.from(indicators)
            .sort(() => 0.5 - Math.random())
            .slice(0, Math.floor(Math.random() * 5) + 1);

          randomIndicators.forEach(indicator => {
            if (Math.random() > 0.7) { // 30%的概率切换状态
              if (indicator.classList.contains('active')) {
                indicator.classList.remove('active');
                indicator.classList.add('inactive');
              } else {
                indicator.classList.remove('inactive');
                indicator.classList.add('active');
              }
            }
          });
        }, 3000);

        // 当弹窗关闭时停止更新
        const modal = document.getElementById('ioStatusModal');
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
              if (modal.style.display === 'none') {
                clearInterval(updateInterval);
                observer.disconnect();
              }
            }
          });
        });
        observer.observe(modal, { attributes: true });
      }

      /**
       * 显示拓扑图弹窗
       * @param {string} type - 拓扑图类型：'electrical' 或 'cooling'
       */
      function showTopologyModal(type) {
        const modal = document.getElementById('topologyModal');
        const title = document.getElementById('topologyModalTitle');
        const image = document.getElementById('topologyImage');
        const imageContainer = document.querySelector('.topology-image-container');
        const iframeContainer = document.getElementById('topologyIframeContainer');
        const iframe = document.getElementById('topologyIframe');

        if (modal && title && image && imageContainer && iframeContainer && iframe) {
          // 设置标题和内容
          if (type === 'electrical') {
            title.innerHTML = '<i class="fas fa-bolt"></i>电气系统拓扑图';
            image.src = './image/SVG系统拓扑图.png';
            image.alt = '电气系统拓扑图';
            // 显示图片，隐藏iframe
            imageContainer.style.display = 'block';
            iframeContainer.style.display = 'none';
          } else if (type === 'cooling') {
            title.innerHTML = '<i class="fas fa-tint"></i>水冷系统拓扑图';
            // 设置iframe URL
            iframe.src = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=29bd5260-38c5-4b4f-97a0-66e97903f9e9';
            // 隐藏图片，显示iframe
            imageContainer.style.display = 'none';
            iframeContainer.style.display = 'block';
          }

          modal.style.display = 'flex';
          // 添加显示动画
          setTimeout(() => {
            modal.classList.add('show');
          }, 10);
        }
      }

      /**
       * 关闭拓扑图弹窗
       */
      function closeTopologyModal() {
        const modal = document.getElementById('topologyModal');
        const iframe = document.getElementById('topologyIframe');

        if (modal) {
          modal.classList.remove('show');
          setTimeout(() => {
            modal.style.display = 'none';
            // 清理iframe内容以释放资源
            if (iframe) {
              iframe.src = '';
            }
          }, 300);
        }
      }

      /**
       * 显示单元状态弹窗
       */
      function showUnitStatusModal() {
        const modal = document.getElementById('unitStatusModal');
        if (modal) {
          modal.style.display = 'flex';
          // 添加显示动画
          setTimeout(() => {
            modal.classList.add('show');
          }, 10);
          // 初始化单元状态数据
          initUnitStatusData();
        }
      }

      /**
       * 关闭单元状态弹窗
       */
      function closeUnitStatusModal() {
        const modal = document.getElementById('unitStatusModal');
        if (modal) {
          modal.classList.remove('show');
          setTimeout(() => {
            modal.style.display = 'none';
          }, 300);
        }
      }

      /**
       * 显示模块弹窗（修改为在新标签页打开）
       * @param {string} moduleId - 模块ID
       * @param {string} title - 弹窗标题
       * @param {string} iconClass - 图标类名
       * @param {string} iframeUrl - 页面URL地址
       */
      function showModuleModal(moduleId, title, iconClass, iframeUrl = 'https://pic.rmb.bdstatic.com/bjh/down/bddf6d05be23936f9765bbe668e1fa41.gif') {
        // 在新标签页中打开页面
        window.open(iframeUrl, '_blank');
        
        console.log(`在新标签页打开${title}页面`);
      }

      /**
       * 关闭模块弹窗（已不再使用，保留函数以兼容现有代码）
       */
      function closeModuleModal() {
        // 由于模块内容现在在新标签页中打开，此函数不再需要执行任何操作
        console.log('closeModuleModal: 此功能已不再使用');
        // 保留空函数以兼容可能的现有调用
      }

      /**
       * 关闭单元详细信息
       */
      function closeUnitDetail() {
        // 取消选中状态
        const selectedUnit = document.querySelector('.unit-item.selected');
        if (selectedUnit) {
          selectedUnit.classList.remove('selected');
        }
        // 隐藏详细信息面板（可以添加动画效果）
        const detailSection = document.querySelector('.unit-detail-section');
        if (detailSection) {
          detailSection.style.opacity = '0.5';
          setTimeout(() => {
            detailSection.style.opacity = '1';
          }, 200);
        }
      }

      /**
       * 初始化单元状态数据和交互
       */
      function initUnitStatusData() {
        // 为单元项添加点击事件
        const unitItems = document.querySelectorAll('.unit-item');
        unitItems.forEach(item => {
          item.addEventListener('click', function() {
            // 移除其他选中状态
            unitItems.forEach(u => u.classList.remove('selected'));
            // 添加选中状态
            this.classList.add('selected');

            // 更新详细信息
            const unitId = this.dataset.unit;
            updateUnitDetail(unitId);
          });
        });

        // 模拟实时数据更新
        simulateUnitStatusUpdate();
      }

      /**
       * 更新单元详细信息
       * @param {string} unitId - 单元ID
       */
      function updateUnitDetail(unitId) {
        const titleElement = document.getElementById('selectedUnitTitle');
        if (titleElement) {
          titleElement.textContent = `${unitId} 单元详细状态`;
        }

        // 模拟不同单元的状态数据
        const statusData = getUnitStatusData(unitId);
        const statusItems = document.querySelectorAll('.unit-detail-section .status-item');

        statusItems.forEach((item, index) => {
          const indicator = item.querySelector('.status-indicator');
          if (indicator && statusData[index] !== undefined) {
            if (statusData[index]) {
              indicator.classList.remove('inactive');
              indicator.classList.add('active');
            } else {
              indicator.classList.remove('active');
              indicator.classList.add('inactive');
            }
          }
        });
      }

      /**
       * 获取单元状态数据（模拟数据）
       * @param {string} unitId - 单元ID
       * @returns {Array} 状态数组
       */
      function getUnitStatusData(unitId) {
        // 模拟不同单元的状态数据
        const statusPatterns = {
          'A01': [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false],
          'A02': [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false],
          'A03': [false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false],
          'A12': [false, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false]
        };

        return statusPatterns[unitId] || statusPatterns['A12'];
      }

      /**
       * 模拟单元状态实时更新
       */
      function simulateUnitStatusUpdate() {
        const updateInterval = setInterval(() => {
          // 随机更新单元电压值
          const unitItems = document.querySelectorAll('.unit-item');
          unitItems.forEach(item => {
            const voltageSpan = item.querySelector('.unit-voltage');
            if (voltageSpan && Math.random() > 0.8) { // 20%概率更新
              const baseVoltage = parseInt(voltageSpan.textContent);
              const variation = Math.floor(Math.random() * 6) - 3; // -3到+3的变化
              const newVoltage = Math.max(735, Math.min(745, baseVoltage + variation));
              voltageSpan.textContent = newVoltage.toString();
            }
          });

          // 随机更新指示器状态
          const indicators = document.querySelectorAll('.unit-item .indicator-dot');
          indicators.forEach(indicator => {
            if (Math.random() > 0.9) { // 10%概率切换状态
              if (indicator.classList.contains('active')) {
                indicator.classList.remove('active');
                indicator.classList.add('inactive');
              } else {
                indicator.classList.remove('inactive');
                indicator.classList.add('active');
              }
            }
          });

          // 更新电压统计显示
          updateVoltageStats();
        }, 2000);

        // 当弹窗关闭时停止更新
        const modal = document.getElementById('unitStatusModal');
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
              if (modal.style.display === 'none') {
                clearInterval(updateInterval);
                observer.disconnect();
              }
            }
          });
        });
        observer.observe(modal, { attributes: true });
      }

      /**
       * 更新电压统计显示
       */
      function updateVoltageStats() {
        const voltageValues = document.querySelectorAll('.voltage-value');
        voltageValues.forEach(valueElement => {
          if (Math.random() > 0.7) { // 30%概率更新
            const currentValue = parseInt(valueElement.textContent);
            const variation = Math.floor(Math.random() * 4) - 2; // -2到+2的变化
            const newValue = Math.max(735, Math.min(745, currentValue + variation));
            valueElement.textContent = `${newValue} V`;
          }
        });
      }

      /**
       * 初始化弹窗事件监听
       */
      function initModalEvents() {
        // 点击弹窗外部区域关闭弹窗
        document.addEventListener('click', function(event) {
          // I/O状态弹窗
          const ioModal = document.getElementById('ioStatusModal');
          if (ioModal && ioModal.style.display === 'flex') {
            if (event.target === ioModal) {
              closeIOStatusModal();
            }
          }

          // 拓扑图弹窗
          const topologyModal = document.getElementById('topologyModal');
          if (topologyModal && topologyModal.style.display === 'flex') {
            if (event.target === topologyModal) {
              closeTopologyModal();
            }
          }

          // 单元状态弹窗
          const unitModal = document.getElementById('unitStatusModal');
          if (unitModal && unitModal.style.display === 'flex') {
            if (event.target === unitModal) {
              closeUnitStatusModal();
            }
          }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(event) {
          if (event.key === 'Escape') {
            const ioModal = document.getElementById('ioStatusModal');
            const topologyModal = document.getElementById('topologyModal');
            const unitModal = document.getElementById('unitStatusModal');

            if (ioModal && ioModal.style.display === 'flex') {
              closeIOStatusModal();
            }

            if (topologyModal && topologyModal.style.display === 'flex') {
              closeTopologyModal();
            }

            if (unitModal && unitModal.style.display === 'flex') {
              closeUnitStatusModal();
            }
          }
        });
      }
    </script>
  </body>
</html>
